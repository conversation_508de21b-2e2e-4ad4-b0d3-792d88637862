package main

import (
	"log"
	"regexp"
	"strconv"
)

// WindowsPatchRest represents a patch with relevant details.
type WindowsPatchRest struct {
	KbId  string
	UUID  string
	Title string
}

func extractVersion(versionStr string) int {
	re := regexp.MustCompile(`(\d+)H\d`)
	match := re.FindStringSubmatch(versionStr)
	if len(match) > 1 {
		if num, err := strconv.Atoi(match[1]); err == nil {
			return num
		}
	}
	return 0
}

func main() {
	displayVersion := "Windows 23H2 Security Patch" // Example display version

	// Example patch data
	missingPatchMap := map[string]WindowsPatchRest{
		"UUID1": {KbId: "KB12345", UUID: "UUID1", Title: "Windows 23H2 Update"},
		"UUID2": {KbId: "KB67890", UUID: "UUID2", Title: "Windows 25H1 Security Patch"},
		"UUID3": {KbId: "KB67891", UUID: "UUID3", Title: "Windows 22H1 Security Patch"},
		"UUID4": {KbId: "KB67892", UUID: "UUID4", Title: "Windows 24H1 Security Patch"},
		"UUID5": {KbId: "KB67893", UUID: "UUID5", Title: "Windows 24H2 Security Patch"},
		"UUID6": {KbId: "KB67894", UUID: "UUID6", Title: "Windows Update Security Patch"},
	}

	tempmissingPatchMap := make(map[string]WindowsPatchRest)
	for k, v := range missingPatchMap {
		tempmissingPatchMap[k] = v
	}

	//kbToUUIDMap := map[string]WindowsPatchRest{}
	currentVersion := extractVersion(displayVersion)

	for _, patchRest := range missingPatchMap {
		log.Println("Checking missing patch to remove due to Display version:", displayVersion, ", Patch title:", patchRest.Title)
		patchVersion := extractVersion(patchRest.Title)
		if patchVersion > 0 {
			if patchVersion > currentVersion {
				log.Println("Removing patch due to higher version:", patchRest.Title)
				delete(missingPatchMap, patchRest.UUID)
				delete(tempmissingPatchMap, patchRest.UUID)
			}
		}

	}

	// Remaining patches after filtering
	log.Println("Remaining patches:", missingPatchMap)
}
