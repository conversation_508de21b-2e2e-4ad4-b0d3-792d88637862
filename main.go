package main

import (
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	_ "os"
	"regexp"
	"strings"
)

const (
	UpdateCatalogUrl = "https://www.catalog.update.microsoft.com/"
)

func main() {
	downloadInfos := getDownloadUrlsByWindowsPatchUUID("AED7F6E7-01E6-4265-9B39-2A665BFBFE93")
	fmt.Printf("Total downloads found: %d\n", len(downloadInfos))
}

func getDownloadUrlsByWindowsPatchUUID(uuid string) []map[string]string {
	data := fmt.Sprintf("[{\"size\":0,\"languages\":\"\",\"uidInfo\":\"%s\",\"updateID\":\"%s\"}]", uuid, uuid)
	resp, err := http.Post(UpdateCatalogUrl+"DownloadDialog.aspx", "application/x-www-form-urlencoded;charset=UTF-8", strings.NewReader("updateIDs="+data))
	if err != nil {
		return nil
	}
	defer func(Body io.ReadCloser) {
		err = Body.Close()
		if err != nil {
		}
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil
	}
	htmlContent := string(body)
	// Single regex to capture all download information
	// SHA256 values are base64 encoded, not hex encoded
	regex := regexp.MustCompile(`downloadInformation\[(\d+)\]\.files\[(\d+)\]\.(?:url\s*=\s*'([^']+)'|sha256\s*=\s*'([A-Za-z0-9+/=]+)')`)

	matches := regex.FindAllStringSubmatch(htmlContent, -1)

	// Map to store download info by index
	downloadMap := make(map[string]map[string]string)

	for _, match := range matches {
		if len(match) >= 5 {
			downloadIndex := match[1]
			fileIndex := match[2]
			key := downloadIndex + "_" + fileIndex

			// Initialize if not exists
			if downloadMap[key] == nil {
				downloadMap[key] = make(map[string]string)
			}

			// Check which field was matched and assign accordingly
			if match[3] != "" { // URL
				downloadMap[key]["url"] = match[3]
			} else if match[4] != "" { // SHA256 (base64)
				downloadMap[key]["sha256_base64"] = match[4]
				// Convert base64 to hex
				if decoded, err := base64.StdEncoding.DecodeString(match[4]); err == nil {
					downloadMap[key]["sha256_hex"] = hex.EncodeToString(decoded)
				}
			}
		}
	}

	// Convert map to slice
	var downloadInfos []map[string]string
	for _, info := range downloadMap {
		if info["url"] != "" { // Only include entries with URLs
			downloadInfos = append(downloadInfos, info)
		}
	}

	// Print results for debugging
	fmt.Printf("Found %d download entries:\n", len(downloadInfos))
	for i, info := range downloadInfos {
		fmt.Printf("  %d: URL: %s\n", i+1, info["url"])
		fmt.Printf("      SHA256 (Base64): %s\n", info["sha256_base64"])
		fmt.Printf("      SHA256 (Hex): %s\n", info["sha256_hex"])
		fmt.Printf("\n")
	}

	return downloadInfos
}
