package main

import (
	"fmt"
	"io"
	"net/http"
	_ "os"
	"regexp"
	"strings"
)

const (
	UpdateCatalogUrl = "https://www.catalog.update.microsoft.com/"
)

type DownloadInfo struct {
	URL    string
	SHA1   string
	SHA256 string
}

func main() {
	downloadInfos := getDownloadUrlsByWindowsPatchUUID("AED7F6E7-01E6-4265-9B39-2A665BFBFE93")
	fmt.Printf("Total downloads found: %d\n", len(downloadInfos))
}

func getDownloadUrlsByWindowsPatchUUID(uuid string) []DownloadInfo {
	data := fmt.Sprintf("[{\"size\":0,\"languages\":\"\",\"uidInfo\":\"%s\",\"updateID\":\"%s\"}]", uuid, uuid)
	resp, err := http.Post(UpdateCatalogUrl+"DownloadDialog.aspx", "application/x-www-form-urlencoded;charset=UTF-8", strings.NewReader("updateIDs="+data))
	if err != nil {
		return nil
	}
	defer func(Body io.ReadCloser) {
		err = Body.Close()
		if err != nil {
		}
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil
	}
	htmlContent := string(body)

	// Single regex to capture all download information
	// This regex looks for downloadInformation[index].files[index] blocks and captures URL, SHA1, and SHA256
	regex := regexp.MustCompile(`downloadInformation\[(\d+)\]\.files\[(\d+)\]\.(?:url\s*=\s*'([^']+)'|digest\s*=\s*'([a-fA-F0-9]{40})'|sha256\s*=\s*'([a-fA-F0-9]{64})')`)

	matches := regex.FindAllStringSubmatch(htmlContent, -1)

	// Map to store download info by index
	downloadMap := make(map[string]*DownloadInfo)

	for _, match := range matches {
		if len(match) >= 6 {
			downloadIndex := match[1]
			fileIndex := match[2]
			key := downloadIndex + "_" + fileIndex

			// Initialize if not exists
			if downloadMap[key] == nil {
				downloadMap[key] = &DownloadInfo{}
			}

			// Check which field was matched and assign accordingly
			if match[3] != "" { // URL
				downloadMap[key].URL = match[3]
			} else if match[4] != "" { // SHA1
				downloadMap[key].SHA1 = match[4]
			} else if match[5] != "" { // SHA256
				downloadMap[key].SHA256 = match[5]
			}
		}
	}

	// Convert map to slice
	var downloadInfos []DownloadInfo
	for _, info := range downloadMap {
		if info.URL != "" { // Only include entries with URLs
			downloadInfos = append(downloadInfos, *info)
		}
	}

	// Print results for debugging
	fmt.Printf("Found %d download entries:\n", len(downloadInfos))
	for i, info := range downloadInfos {
		fmt.Printf("  %d: URL: %s\n", i+1, info.URL)
		fmt.Printf("      SHA1: %s\n", info.SHA1)
		fmt.Printf("      SHA256: %s\n", info.SHA256)
		fmt.Printf("\n")
	}

	return downloadInfos
}
