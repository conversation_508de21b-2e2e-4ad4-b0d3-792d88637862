package main

import (
	"fmt"
	"io"
	"net/http"
	_ "os"
	"regexp"
	"strings"
)

const (
	UpdateCatalogUrl = "https://www.catalog.update.microsoft.com/"
)

func main() {
	getDownloadUrlsByWindowsPatchUUID("AED7F6E7-01E6-4265-9B39-2A665BFBFE93")
}

func getDownloadUrlsByWindowsPatchUUID(uuid string) (downloadUrls []string, sha1Checksum string, sha256Checksum string) {
	data := fmt.Sprintf("[{\"size\":0,\"languages\":\"\",\"uidInfo\":\"%s\",\"updateID\":\"%s\"}]", uuid, uuid)
	resp, err := http.Post(UpdateCatalogUrl+"DownloadDialog.aspx", "application/x-www-form-urlencoded;charset=UTF-8", strings.NewReader("updateIDs="+data))
	if err != nil {
		return downloadUrls, sha1Checksum, sha256Checksum
	}
	defer func(Body io.ReadCloser) {
		err = Body.Close()
		if err != nil {
		}
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return downloadUrls, sha1Checksum, sha256Checksum
	}
	htmlContent := string(body)

	// Extract download URLs
	urlRegex := regexp.MustCompile(`downloadInformation\[\d+\]\.files\[\d+\]\.url\s*=\s*'([^']+)'`)
	urlMatches := urlRegex.FindAllStringSubmatch(htmlContent, -1)
	for _, match := range urlMatches {
		if len(match) > 1 {
			downloadUrls = append(downloadUrls, match[1])
		}
	}

	// Extract SHA1 checksum
	sha1Regex := regexp.MustCompile(`downloadInformation\[\d+\]\.files\[\d+\]\.digest\s*=\s*'([a-fA-F0-9]{40})'`)
	sha1Matches := sha1Regex.FindStringSubmatch(htmlContent)
	if len(sha1Matches) > 1 {
		sha1Checksum = sha1Matches[1]
	}

	// Extract SHA256 checksum
	sha256Regex := regexp.MustCompile(`downloadInformation\[\d+\]\.files\[\d+\]\.sha256\s*=\s*'([a-fA-F0-9]{64})'`)
	sha256Matches := sha256Regex.FindAllStringSubmatch(htmlContent)
	if len(sha256Matches) > 1 {
		sha256Checksum = sha256Matches[1]
	}

	// Print results for debugging
	fmt.Printf("Found %d download URLs:\n", len(downloadUrls))
	for i, url := range downloadUrls {
		fmt.Printf("  %d: %s\n", i+1, url)
	}
	fmt.Printf("SHA1: %s\n", sha1Checksum)
	fmt.Printf("SHA256: %s\n", sha256Checksum)

	return downloadUrls, sha1Checksum, sha256Checksum
}
