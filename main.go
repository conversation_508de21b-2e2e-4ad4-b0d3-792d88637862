package main

import (
	"fmt"
	"io"
	"net/http"
	_ "os"
	"regexp"
	"strings"
)

const (
	UpdateCatalogUrl = "https://www.catalog.update.microsoft.com/"
)

func main() {
	getDownloadUrlsByWindowsPatchUUID("AED7F6E7-01E6-4265-9B39-2A665BFBFE93")
}

func getDownloadUrlsByWindowsPatchUUID(uuid string) (downloadUrls []string, sha1Checksum string, sha256Checksum string) {
	data := fmt.Sprintf("[{\"size\":0,\"languages\":\"\",\"uidInfo\":\"%s\",\"updateID\":\"%s\"}]", uuid, uuid)
	resp, err := http.Post(UpdateCatalogUrl+"DownloadDialog.aspx", "application/x-www-form-urlencoded;charset=UTF-8", strings.NewReader("updateIDs="+data))
	if err != nil {
		return downloadUrls, sha1Checksum, sha256Checksum
	}
	defer func(Body io.ReadCloser) {
		err = Body.Close()
		if err != nil {
		}
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return downloadUrls, sha1Checksum, sha256Checksum
	}
	htmlContent := string(body)

	scriptTags := strings.Split(htmlContent, "<script")
	for _, tag := range scriptTags {
		re := regexp.MustCompile("(downloadInformation)\\[\\d+\\]\\.files\\[\\d+\\].url = \\'[(http(s)?):\\/\\/(www\\.)?a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)\\'")
		matches := re.FindStringSubmatch(tag)
		if len(matches) > 1 {
			url := matches[0]
			url = strings.Split(url, "=")[1]
			url = strings.Trim(url, " ")
			url = strings.Trim(url, "'")
			downloadUrls = append(downloadUrls, url)
		}
	}

	return downloadUrls, sha1Checksum, sha256Checksum
}
