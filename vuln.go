package main

import (
	"database/sql"
	"fmt"
	"github.com/doug-martin/goqu/v9"
	"github.com/jmoiron/sqlx"
	_ "github.com/mattn/go-sqlite3"

	"macXmlParsing/fleet"
	"macXmlParsing/nvd"
	"macXmlParsing/nvd/wfn"
	"path/filepath"
	"slices"
	"strings"
	"unicode"
)

func main() {
	dbPath := filepath.Join("/home/<USER>/Downloads/cpe.sqlite")

	/*var software = fleet.Software{
		Name:    "TeamViewer 14",
		Version: "14.7.48796",
		Source:  "programs",
		Vendor:  "TeamViewer",
	}*/

	/*var software = fleet.Software{
		Name:    "WinSCP 6.1.2",
		Version: "6.1.2",
		Source:  "programs",
		Vendor:  "<PERSON>",
	}*/

	/*var software = fleet.Software{
		Name:    "Microsoft Visual C++ 2008 Redistributable - x64 9.0.21022",
		Version: "9.0.21022",
		Source:  "programs",
		Vendor:  "Microsoft Corporation",
	}*/

	/*var software = fleet.Software{
		Name:             "App Store.app",
		Version:          "3.0",
		Source:           "programs",
		BundleIdentifier: "com.apple.AppStore",
	}*/

	/*var software = fleet.Software{
		Name:    "Git",
		Version: "2.46.2",
		Source:  "programs",
		Vendor:  "The Git Development Community",
	}*/

	var software = fleet.Software{
		Name:    "GoLand 2024.2.1",
		Version: "242.21829.165",
		Source:  "programs",
		Vendor:  "JetBrains s.r.o.",
	}

	db, err := sqliteDB(dbPath)
	if err != nil {
		fmt.Println("Error opening database:", err)
	}
	defer db.Close()

	var cpe string

	cpe, err = CPEFromSoftware(db, &software)

	fmt.Println("CPE:", cpe)
}

type IndexedCPEItem struct {
	ID         int `json:"id" db:"rowid"`
	Part       string
	Product    string `json:"product" db:"product"`
	Vendor     string `json:"vendor" db:"vendor"`
	Deprecated bool   `json:"deprecated" db:"deprecated"`
	Weight     int    `db:"weight"`
}

func CPEFromSoftware(db *sqlx.DB, software *fleet.Software) (string, error) {
	if containsNonASCII(software.Name) {
		return "", nil
	}

	ds := goqu.Dialect("sqlite").From(goqu.I("cpe_2").As("c")).
		Select(
			"c.rowid",
			"c.product",
			"c.vendor",
			"c.deprecated",
			goqu.L("1 as weight"),
		).Limit(1)

	var exps []goqu.Expression
	exps = append(exps, goqu.I("c.product").Eq("7-zip"))

	ds = ds.Where(goqu.Or(exps...))

	var exps1 []goqu.Expression
	exps1 = append(exps1, goqu.I("c.vendor").Eq("7-zip"))

	ds = ds.Where(goqu.Or(exps1...))

	stm, args, err := ds.ToSQL()
	fmt.Println(stm)

	/*stm, args, err := cpeGeneralSearchQuery(software)
	if err != nil {
		return "", fmt.Errorf("getting cpes for: %s: %w", software.Name, err)
	}*/

	var results []IndexedCPEItem
	var match *IndexedCPEItem

	err = db.Select(&results, stm, args...)
	if err == sql.ErrNoRows {
		return "", nil
	}

	if err != nil {
		return "", fmt.Errorf("getting cpes for: %s: %w", software.Name, err)
	}

	for i, item := range results {
		hasAllTerms := true

		sName := strings.ToLower(software.Name)
		for _, sN := range strings.Split(item.Product, "_") {
			hasAllTerms = hasAllTerms && strings.Index(sName, sN) != -1
		}

		sVendor := strings.ToLower(software.Vendor)
		sBundle := strings.ToLower(software.BundleIdentifier)
		for _, sV := range strings.Split(item.Vendor, "_") {
			if sVendor != "" {
				hasAllTerms = hasAllTerms && strings.Index(sVendor, sV) != -1
			}

			if sBundle != "" {
				hasAllTerms = hasAllTerms && strings.Index(sBundle, sV) != -1
			}
		}

		if hasAllTerms {
			match = &results[i]
			break
		}
	}

	if match != nil {
		if !match.Deprecated {
			return match.FmtStr(software), nil
		}

		// try to find a non-deprecated cpe by looking up deprecated_by
		for _, item := range results {
			deprecatedItem := item
			for {
				var deprecation IndexedCPEItem

				err = db.Get(
					&deprecation,
					`
						SELECT
							rowid,
							product,
							vendor,
							deprecated
						FROM
							cpe_2
						WHERE
							cpe23 IN (
								SELECT cpe23 FROM deprecated_by d WHERE d.cpe_id = ?
							)
					`,
					deprecatedItem.ID,
				)
				if err == sql.ErrNoRows {
					break
				}
				if err != nil {
					return "", fmt.Errorf("getting deprecation: %w", err)
				}
				if deprecation.Deprecated {
					deprecatedItem = deprecation
					continue
				}

				return deprecation.FmtStr(software), nil
			}
		}
	}

	return "", nil
}

func (i *IndexedCPEItem) FmtStr(s *fleet.Software) string {
	cpe := wfn.NewAttributesWithAny()
	cpe.Part = "a"
	cpe.Vendor = i.Vendor
	cpe.Product = i.Product
	cpe.Version = nvd.SanitizeVersion(s.Version)
	cpe.TargetSW = nvd.TargetSW(s)

	if i.Part != "" {
		cpe.Part = i.Part
	}

	// Make sure we don't return a 'match all' CPE
	if cpe.Vendor == wfn.Any || cpe.Product == wfn.Any {
		return ""
	}

	return cpe.BindToFmtString()
}

var allowedNonASCII = []int32{
	'–', // en dash
	'—', // em dash
}

func containsNonASCII(s string) bool {
	for _, char := range s {
		if char > unicode.MaxASCII && !slices.Contains(allowedNonASCII, char) {
			return true
		}
	}
	return false
}

func sqliteDB(dbPath string) (*sqlx.DB, error) {
	db, err := sqlx.Open("sqlite3", dbPath)
	if err != nil {
		return nil, err
	}
	return db, nil
}

func cpeGeneralSearchQuery(software *fleet.Software) (string, []interface{}, error) {
	dialect := goqu.Dialect("sqlite")

	// 1 - Try to match product and vendor terms
	search1 := dialect.From(goqu.I("cpe_2").As("c")).
		Select("c.rowid", "c.product", "c.vendor", "c.deprecated", goqu.L("1 as weight"))
	var vexps []goqu.Expression
	for _, v := range nvd.VendorVariations(software) {
		vexps = append(vexps, goqu.I("c.vendor").Eq(v))
	}
	search1 = search1.Where(goqu.Or(vexps...))
	var nexps []goqu.Expression
	for _, v := range nvd.ProductVariations(software) {
		nexps = append(nexps, goqu.I("c.product").Eq(v))
	}
	search1 = search1.Where(goqu.Or(nexps...))

	// 2 - Try to match product only
	search2 := dialect.From(goqu.I("cpe_2").As("c")).
		Select("c.rowid", "c.product", "c.vendor", "c.deprecated", goqu.L("2 as weight")).
		Where(goqu.L("c.product = ?", nvd.SanitizeSoftwareName(software)))

	// 3 - Try Full text match
	search3 := dialect.From(goqu.I("cpe_2").As("c")).
		Select("c.rowid", "c.product", "c.vendor", "c.deprecated", goqu.L("3 as weight")).
		Join(
			goqu.I("cpe_search").As("cs"),
			goqu.On(goqu.I("cs.rowid").Eq(goqu.I("c.rowid"))),
		).
		Where(goqu.L("cs.title like ? ", nvd.SanitizeMatch(software.Name)))

	datasets := []*goqu.SelectDataset{search1, search2, search3}

	// 4 - Try vendor/product from bundle identifier, like tld.vendor.product
	bundleParts := strings.Split(software.BundleIdentifier, ".")
	if len(bundleParts) == 3 {
		search4 := dialect.From(goqu.I("cpe_2").As("c")).
			Select("c.rowid", "c.product", "c.vendor", "c.deprecated", goqu.L("4 as weight")).
			Where(
				goqu.L("c.vendor = ?", strings.ToLower(bundleParts[1])), goqu.L("c.product = ?", strings.ToLower(bundleParts[2])),
			)
		datasets = append(datasets, search4)
	}

	var sqlParts []string
	var args []interface{}
	var stm string

	for _, d := range datasets {
		s, a, err := d.ToSQL()
		if err != nil {
			return "", nil, fmt.Errorf("sql: %w", err)
		}
		sqlParts = append(sqlParts, s)
		args = append(args, a...)
	}

	stm = strings.Join(sqlParts, " UNION ")
	stm += "ORDER BY weight ASC"
	fmt.Println(stm)

	return stm, args, nil
}
